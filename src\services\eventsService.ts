import {
  Event,
  EventCreator,
  EventSubscription,
  EventPhotographer,
  EventPermissions,
  EventNavigation,
  EventPhoto,
  EventUploadPermissions
} from '../types/events'

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000'

export interface EventPhotosResponse {
  count: number
  photos: EventPhoto[]
}

// Main Event interface matching API response
export type ApiEvent = Event

// Request interfaces
export interface CreateEventRequest {
  name: string
  description: string
  location: string
  event_type: 'WEDDING' | 'CORPORATE' | 'SPORTS' | 'PARTY' | 'CONFERENCE' | 'OTHER'
  start_date: string
  end_date: string
  visibility: 'PUBLIC' | 'PRIVATE'
  image_price_limit: string
  subscription: number
}

export interface JoinEventRequest {
  portfolio_link?: string
  specialization?: string
}

export interface JoinEventResponse {
  status: string
  message: string
  photographer_role: {
    role: string
    assigned_via: string
    can_upload_photos: boolean
    can_set_photo_prices: boolean
    can_view_earnings: boolean
  }
}

export interface SearchEventsRequest {
  query?: string
  event_type?: string
  location?: string
  date?: string
}

export interface NearbyEventsRequest {
  latitude: number
  longitude: number
  radius: number
}

export interface NearbyEvent {
  id: string
  name: string
  location: string
  distance: number
  start_date: string
  event_type: string
}

// Events API Service Class
class EventsService {
  private async makeRequest(endpoint: string, token: string, options: RequestInit = {}) {
    const url = `${API_BASE_URL}/api/v1/events${endpoint}`

    // Log the request (Flutter-style)
    console.log('\n🌐 EVENTS API REQUEST:')
    console.log(`📍 ${options.method || 'GET'} ${url}`)
    console.log('🔐 Authorization: Bearer ***' + token.substring(token.length - 10))
    if (options.body) {
      console.log('📤 Request Body:', JSON.parse(options.body as string))
    }
    console.log('⏰ Time:', new Date().toLocaleTimeString())

    const startTime = Date.now()
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers,
      },
      ...options,
    })

    const responseTime = Date.now() - startTime
    console.log(`📥 Response: ${response.status} ${response.statusText} (${responseTime}ms)`)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Network error' }))
      console.error('❌ API Error:', errorData)
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.log('✅ Response Data:', data)
    console.log('─'.repeat(50))

    return data
  }

  // 1. Get All Events (Photographer Events)
  async getAllEvents(token: string): Promise<ApiEvent[]> {
    return this.makeRequest('/photographer_events/', token)
  }

  // 2. Get Home Events (User's Personalized Events)
  async getHomeEvents(token: string): Promise<ApiEvent[]> {
    return this.makeRequest('/home/<USER>/', token)
  }

  // 3. Get User's Attended Events
  async getAttendedEvents(token: string): Promise<ApiEvent[]> {
    return this.makeRequest('/attended/', token)
  }

  // 4. Get User's Created Events
  async getCreatedEvents(token: string): Promise<ApiEvent[]> {
    return this.makeRequest('/created/', token)
  }

  // 5. Get Event Details (Role-Based Response)
  async getEventDetails(eventId: string, token: string): Promise<ApiEvent> {
    return this.makeRequest(`/${eventId}/`, token)
  }

  // 6. Join Event
  async joinEvent(eventId: string, token: string, data: JoinEventRequest): Promise<JoinEventResponse> {
    return this.makeRequest(`/${eventId}/join_as_photographer/`, token, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  // 7. Leave Event
  async leaveEvent(eventId: string, token: string): Promise<{ status: string }> {
    return this.makeRequest(`/${eventId}/leave/`, token, {
      method: 'POST',
    })
  }

  // 8. Search Events
  async searchEvents(token: string, searchData: SearchEventsRequest): Promise<ApiEvent[]> {
    return this.makeRequest('/search/', token, {
      method: 'POST',
      body: JSON.stringify(searchData),
    })
  }

  // 9. Get Nearby Events
  async getNearbyEvents(token: string, locationData: NearbyEventsRequest): Promise<NearbyEvent[]> {
    return this.makeRequest('/home/<USER>/', token, {
      method: 'POST',
      body: JSON.stringify(locationData),
    })
  }

  // 10. Create Event
  async createEvent(token: string, eventData: CreateEventRequest): Promise<ApiEvent> {
    return this.makeRequest('/', token, {
      method: 'POST',
      body: JSON.stringify(eventData),
    })
  }

  // 11. Get Event Photos
  async getEventPhotos(eventId: string, token: string): Promise<EventPhotosResponse> {
    return this.makeRequest(`/${eventId}/photos/`, token)
  }
}

export const eventsService = new EventsService()
